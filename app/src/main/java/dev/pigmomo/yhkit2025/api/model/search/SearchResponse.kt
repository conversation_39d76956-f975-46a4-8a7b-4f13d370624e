package dev.pigmomo.yhkit2025.api.model.search

/**
 * 搜索响应数据模型
 */
data class SearchResponse(
    val code: Int,
    val message: String,
    val data: SearchData?
)

/**
 * 搜索数据
 */
data class SearchData(
    val skuSaleTypes: List<String>?,
    val pageBase: PageBase?,
    val total: Int?,
    val page: Int?,
    val pagecount: Int?,
    val totalpage: Int?,
    val searchresulttype: Int?,
    val results: List<SearchResult>?
)

/**
 * 页面基础信息
 */
data class PageBase(
    val id: String?,
    val pageT: String?,
    val pageSt: String?,
    val statistics: Statistics?,
    val hasNext: Int?,
    val nextPage: Int?,
    val nextUrl: String?,
    val pageName: String?
)

/**
 * 统计信息
 */
data class Statistics(
    val rpage: String?,
    val traceId: String?
)

/**
 * 搜索结果项
 */
data class SearchResult(
    val blockId: String?,
    val statistic: Statistic?,
    val blockType: Int?,
    val skuBlock: SkuBlock?
)

/**
 * 统计信息
 */
data class Statistic(
    val aid: String?,
    val cid: String?
)

/**
 * SKU块信息
 */
data class SkuBlock(
    val categoryInfo: CategoryInfo?,
    val type: Int?,
    val action: String?,
    val skuCode: String?,
    val skuType: Int?,
    val skuSaleType: Int?,
    val cover: Cover?,
    val price: Price?,
    val batch: Batch?,
    val tag: Tag?,
    val spu: Spu?,
    val title: String?,
    val subTitle: String?,
    val recAttribute: List<String>?,
    val recSlogan: String?,
    val recSloganType: String?,
    val inStock: Int?,
    val tracking: Tracking?,
    val preprocess: Int?,
    val isOnSale: Boolean?,
    val skuProperty: Int?,
    val brandId: String?,
    val brandName: String?
)

/**
 * 分类信息
 */
data class CategoryInfo(
    val categoryId: String?
)

/**
 * 封面图片
 */
data class Cover(
    val imageUrl: String?
)

/**
 * 价格信息
 */
data class Price(
    val price: String?
)

/**
 * 批次信息
 */
data class Batch(
    val batchFlag: Int?
)

/**
 * 标签信息
 */
data class Tag(
    val commonTags: List<String>?
)

/**
 * SPU信息
 */
data class Spu(
    val isSpu: Int?
)

/**
 * 跟踪信息
 */
data class Tracking(
    val priceInCent: Int?,
    val marketPriceInCent: Int?,
    val point: Int?
)
